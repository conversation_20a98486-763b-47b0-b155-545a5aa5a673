using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
    [Head<PERSON>("Layer Assignment")]
    [Tooltip("Target layer to change texture on")]
    public TerrainLayer targetLayer;

    [Tooltip("Source layer texture to apply")]
    public TerrainLayer sourceLayer;

    [Header("Apply Control")]
    [Tooltip("Change this value to apply texture")]
    public int applyTexture = 0;

    [Head<PERSON>("Terrain Settings")]
    public Terrain targetTerrain;
    public float paintRadius = 5f;
    public float brushStrength = 1f;

    private TerrainData terrainData;
    private int lastApplyValue = 0;
    private int targetLayerIndex = -1;
    private int sourceLayerIndex = -1;

    void Start()
    {
        InitializeTerrain();
    }

    void Update()
    {
        // Check if apply texture value changed
        if (applyTexture != lastApplyValue)
        {
            ApplyTextureToTerrain();
            lastApplyValue = applyTexture;
        }

        // Handle input for painting
        HandleInput();
    }

    void InitializeTerrain()
    {
        if (targetTerrain == null)
            targetTerrain = Terrain.activeTerrain;

        if (targetTerrain != null)
        {
            terrainData = targetTerrain.terrainData;
            FindLayerIndices();
        }
    }

    void FindLayerIndices()
    {
        if (terrainData == null) return;

        TerrainLayer[] layers = terrainData.terrainLayers;

        // Find target layer index
        targetLayerIndex = -1;
        if (targetLayer != null)
        {
            for (int i = 0; i < layers.Length; i++)
            {
                if (layers[i] == targetLayer)
                {
                    targetLayerIndex = i;
                    break;
                }
            }
        }

        // Find source layer index
        sourceLayerIndex = -1;
        if (sourceLayer != null)
        {
            for (int i = 0; i < layers.Length; i++)
            {
                if (layers[i] == sourceLayer)
                {
                    sourceLayerIndex = i;
                    break;
                }
            }
        }
    }

    void HandleInput()
    {
        if (terrainData == null || targetLayerIndex == -1) return;

        // For mobile - use touch input
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            if (touch.phase == TouchPhase.Moved || touch.phase == TouchPhase.Stationary)
            {
                Vector3 touchWorldPos = GetWorldPositionFromTouch(touch.position);
                if (touchWorldPos != Vector3.zero)
                    PaintTextureAtPosition(touchWorldPos);
            }
        }

        // For editor testing - use mouse input
        if (Input.GetMouseButton(0))
        {
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                if (hit.collider.GetComponent<Terrain>())
                    PaintTextureAtPosition(hit.point);
            }
        }
    }

    Vector3 GetWorldPositionFromTouch(Vector2 screenPosition)
    {
        Camera cam = Camera.main;
        if (cam == null) return Vector3.zero;

        Ray ray = cam.ScreenPointToRay(screenPosition);
        if (Physics.Raycast(ray, out RaycastHit hit))
        {
            if (hit.collider.GetComponent<Terrain>())
                return hit.point;
        }
        return Vector3.zero;
    }

    void ApplyTextureToTerrain()
    {
        if (terrainData == null) return;

        // Update layer indices in case layers changed
        FindLayerIndices();

        // Validate layers
        if (targetLayerIndex == -1)
        {
            Debug.LogWarning("Target layer not found in terrain!");
            return;
        }

        if (sourceLayerIndex == -1)
        {
            Debug.LogWarning("Source layer not found in terrain!");
            return;
        }

        // Apply source texture to target layer
        ApplyTextureToEntireTerrain();
    }

    void ApplyTextureToEntireTerrain()
    {
        int alphamapWidth = terrainData.alphamapWidth;
        int alphamapHeight = terrainData.alphamapHeight;

        float[,,] alphaMaps = terrainData.GetAlphamaps(0, 0, alphamapWidth, alphamapHeight);

        // Copy source layer texture data to target layer
        for (int x = 0; x < alphamapWidth; x++)
        {
            for (int z = 0; z < alphamapHeight; z++)
            {
                // Get source layer strength
                float sourceStrength = alphaMaps[x, z, sourceLayerIndex];

                // Apply to target layer
                alphaMaps[x, z, targetLayerIndex] = sourceStrength;

                // Optionally, you can clear the source layer
                // alphaMaps[x, z, sourceLayerIndex] = 0f;
            }
        }

        terrainData.SetAlphamaps(0, 0, alphaMaps);
        Debug.Log($"Applied texture from layer {sourceLayerIndex} to layer {targetLayerIndex}");
    }

    void PaintTextureAtPosition(Vector3 worldPos)
    {
        if (terrainData == null) return;

        Vector3 terrainPos = worldPos - targetTerrain.transform.position;

        // Convert world position to alphamap coordinates
        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        int radius = Mathf.RoundToInt(paintRadius);
        int size = radius * 2;

        // Clamp to terrain bounds
        int startX = Mathf.Clamp(mapX - radius, 0, terrainData.alphamapWidth - size);
        int startZ = Mathf.Clamp(mapZ - radius, 0, terrainData.alphamapHeight - size);

        // Ensure size doesn't exceed bounds
        size = Mathf.Min(size, terrainData.alphamapWidth - startX);
        size = Mathf.Min(size, terrainData.alphamapHeight - startZ);

        if (size <= 0) return;

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, size, size);

        // Paint with circular brush
        Vector2 center = new Vector2(radius, radius);

        for (int x = 0; x < size; x++)
        {
            for (int z = 0; z < size; z++)
            {
                Vector2 pos = new Vector2(x, z);
                float distance = Vector2.Distance(pos, center);

                if (distance <= radius)
                {
                    // Calculate brush falloff
                    float falloff = 1f - (distance / radius);
                    falloff = Mathf.Pow(falloff, 2f); // Smooth falloff

                    float strength = brushStrength * falloff;

                    // Apply texture only to target layer
                    for (int layer = 0; layer < terrainData.alphamapLayers; layer++)
                    {
                        if (layer == targetLayerIndex)
                        {
                            alphaMaps[x, z, layer] = Mathf.Lerp(alphaMaps[x, z, layer], 1f, strength);
                        }
                        else
                        {
                            alphaMaps[x, z, layer] = Mathf.Lerp(alphaMaps[x, z, layer], 0f, strength);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }

    // Public methods for external control
    public void SetSelectedLayer(int layerIndex)
    {
        selectedLayerIndex = Mathf.Clamp(layerIndex, 0, maxLayers - 1);
    }

    public void ApplyCurrentLayerToTerrain()
    {
        applyTextureToSelectedLayer = Random.Range(1, 1000); // Trigger application
    }

    public int GetMaxLayers()
    {
        return maxLayers;
    }

    public string GetLayerInfo()
    {
        if (terrainData == null) return "No terrain data";
        return $"Selected Layer: {selectedLayerIndex}/{maxLayers - 1}";
    }
}
