using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
    [Head<PERSON>("Texture Assignment")]
    [Tooltip("Which layer index to change texture on (0, 1, 2, etc.)")]
    public int targetLayerIndex = 0;

    [Tooltip("New texture to apply")]
    public Texture2D newTexture;

    [Header("Apply Control")]
    [Tooltip("Change this value to apply texture")]
    public int applyTexture = 0;

    [Header("Terrain Settings")]
    public Terrain targetTerrain;
    public float paintRadius = 5f;
    public float brushStrength = 1f;

    private TerrainData terrainData;
    private int lastApplyValue = 0;

    void Start()
    {
        InitializeTerrain();
    }

    void Update()
    {
        // Check if apply texture value changed
        if (applyTexture != lastApplyValue)
        {
            ApplyTextureToTerrain();
            lastApplyValue = applyTexture;
        }

        // Handle input for painting
        HandleInput();
    }

    void InitializeTerrain()
    {
        if (targetTerrain == null)
            targetTerrain = Terrain.activeTerrain;

        if (targetTerrain != null)
        {
            terrainData = targetTerrain.terrainData;

            // Clamp target layer index to valid range
            int maxLayers = terrainData.alphamapLayers;
            targetLayerIndex = Mathf.Clamp(targetLayerIndex, 0, maxLayers - 1);
        }
    }

    void HandleInput()
    {
        if (terrainData == null) return;

        // For mobile - use touch input
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            if (touch.phase == TouchPhase.Moved || touch.phase == TouchPhase.Stationary)
            {
                Vector3 touchWorldPos = GetWorldPositionFromTouch(touch.position);
                if (touchWorldPos != Vector3.zero)
                    PaintTextureAtPosition(touchWorldPos);
            }
        }

        // For editor testing - use mouse input
        if (Input.GetMouseButton(0))
        {
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                if (hit.collider.GetComponent<Terrain>())
                    PaintTextureAtPosition(hit.point);
            }
        }
    }

    Vector3 GetWorldPositionFromTouch(Vector2 screenPosition)
    {
        Camera cam = Camera.main;
        if (cam == null) return Vector3.zero;

        Ray ray = cam.ScreenPointToRay(screenPosition);
        if (Physics.Raycast(ray, out RaycastHit hit))
        {
            if (hit.collider.GetComponent<Terrain>())
                return hit.point;
        }
        return Vector3.zero;
    }

    void ApplyTextureToTerrain()
    {
        if (terrainData == null || newTexture == null) return;

        // Validate target layer index
        int maxLayers = terrainData.alphamapLayers;
        if (targetLayerIndex < 0 || targetLayerIndex >= maxLayers)
        {
            Debug.LogWarning($"Invalid target layer index: {targetLayerIndex}. Valid range: 0-{maxLayers - 1}");
            return;
        }

        // Change the texture of the specified layer
        ChangeLayerTexture();
    }

    void ChangeLayerTexture()
    {
        TerrainLayer[] layers = terrainData.terrainLayers;

        // Create a copy of the layers array
        TerrainLayer[] newLayers = new TerrainLayer[layers.Length];

        for (int i = 0; i < layers.Length; i++)
        {
            if (i == targetLayerIndex)
            {
                // Create new terrain layer with new texture
                newLayers[i] = new TerrainLayer();
                newLayers[i].diffuseTexture = newTexture;

                // Copy other properties from original layer
                if (layers[i] != null)
                {
                    newLayers[i].normalMapTexture = layers[i].normalMapTexture;
                    newLayers[i].tileSize = layers[i].tileSize;
                    newLayers[i].tileOffset = layers[i].tileOffset;
                    newLayers[i].specular = layers[i].specular;
                    newLayers[i].metallic = layers[i].metallic;
                    newLayers[i].smoothness = layers[i].smoothness;
                }
                else
                {
                    // Default values if original layer is null
                    newLayers[i].tileSize = Vector2.one * 15f;
                    newLayers[i].tileOffset = Vector2.zero;
                }
            }
            else
            {
                // Keep original layer
                newLayers[i] = layers[i];
            }
        }

        // Apply the new layers to terrain
        terrainData.terrainLayers = newLayers;

        Debug.Log($"Applied new texture to layer {targetLayerIndex}");
    }

    void PaintTextureAtPosition(Vector3 worldPos)
    {
        if (terrainData == null) return;

        Vector3 terrainPos = worldPos - targetTerrain.transform.position;

        // Convert world position to alphamap coordinates
        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        int radius = Mathf.RoundToInt(paintRadius);
        int size = radius * 2;

        // Clamp to terrain bounds
        int startX = Mathf.Clamp(mapX - radius, 0, terrainData.alphamapWidth - size);
        int startZ = Mathf.Clamp(mapZ - radius, 0, terrainData.alphamapHeight - size);

        // Ensure size doesn't exceed bounds
        size = Mathf.Min(size, terrainData.alphamapWidth - startX);
        size = Mathf.Min(size, terrainData.alphamapHeight - startZ);

        if (size <= 0) return;

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, size, size);

        // Paint with circular brush
        Vector2 center = new Vector2(radius, radius);

        for (int x = 0; x < size; x++)
        {
            for (int z = 0; z < size; z++)
            {
                Vector2 pos = new Vector2(x, z);
                float distance = Vector2.Distance(pos, center);

                if (distance <= radius)
                {
                    // Calculate brush falloff
                    float falloff = 1f - (distance / radius);
                    falloff = Mathf.Pow(falloff, 2f); // Smooth falloff

                    float strength = brushStrength * falloff;

                    // Apply texture only to target layer
                    for (int layer = 0; layer < terrainData.alphamapLayers; layer++)
                    {
                        if (layer == targetLayerIndex)
                        {
                            alphaMaps[x, z, layer] = Mathf.Lerp(alphaMaps[x, z, layer], 1f, strength);
                        }
                        else
                        {
                            alphaMaps[x, z, layer] = Mathf.Lerp(alphaMaps[x, z, layer], 0f, strength);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }

    // Public methods for external control
    public void SetTargetLayer(TerrainLayer layer)
    {
        targetLayer = layer;
        FindLayerIndices();
    }

    public void SetSourceLayer(TerrainLayer layer)
    {
        sourceLayer = layer;
        FindLayerIndices();
    }

    public void ApplyCurrentLayerToTerrain()
    {
        applyTexture = Random.Range(1, 1000); // Trigger application
    }

    public int GetTotalLayers()
    {
        return terrainData != null ? terrainData.alphamapLayers : 0;
    }

    public string GetLayerInfo()
    {
        if (terrainData == null) return "No terrain data";

        string targetInfo = targetLayer != null ? targetLayer.name : "None";
        string sourceInfo = sourceLayer != null ? sourceLayer.name : "None";

        return $"Target: {targetInfo} | Source: {sourceInfo}";
    }
}
