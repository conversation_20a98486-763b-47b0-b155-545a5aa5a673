using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
    [Header("Terrain Layer Selection")]
    [Tooltip("Select which terrain layer to modify (0-based index)")]
    public int selectedLayerIndex = 0;

    [Header("Texture Application")]
    [Tooltip("Apply texture to the selected layer (any value to trigger)")]
    public int applyTextureToSelectedLayer = 0;

    [Header("Terrain Settings")]
    public Terrain targetTerrain;
    public float paintRadius = 5f;
    public float brushStrength = 1f;

    [Header("Mobile Optimization")]
    [Tooltip("Update frequency for mobile performance")]
    public float updateInterval = 0.1f;

    private TerrainData terrainData;
    private int lastApplyValue = 0;
    private float lastUpdateTime = 0f;
    private int maxLayers = 0;

    void Start()
    {
        InitializeTerrain();
    }

    void Update()
    {
        // Mobile optimization - limit update frequency
        if (Time.time - lastUpdateTime < updateInterval)
            return;

        lastUpdateTime = Time.time;

        // Check if apply texture value changed
        if (applyTextureToSelectedLayer != lastApplyValue)
        {
            ApplyTextureToSelectedLayer();
            lastApplyValue = applyTextureToSelectedLayer;
        }

        // Handle input for painting
        HandleInput();
    }

    void InitializeTerrain()
    {
        if (targetTerrain == null)
            targetTerrain = Terrain.activeTerrain;

        if (targetTerrain != null)
        {
            terrainData = targetTerrain.terrainData;
            maxLayers = terrainData.alphamapLayers;

            // Clamp selected layer to valid range
            selectedLayerIndex = Mathf.Clamp(selectedLayerIndex, 0, maxLayers - 1);
        }
    }

    void HandleInput()
    {
        if (terrainData == null) return;

        // For mobile - use touch input
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            if (touch.phase == TouchPhase.Moved || touch.phase == TouchPhase.Stationary)
            {
                Vector3 touchWorldPos = GetWorldPositionFromTouch(touch.position);
                if (touchWorldPos != Vector3.zero)
                    PaintTextureAtPosition(touchWorldPos);
            }
        }

        // For editor testing - use mouse input
        if (Input.GetMouseButton(0))
        {
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                if (hit.collider.GetComponent<Terrain>())
                    PaintTextureAtPosition(hit.point);
            }
        }
    }

    Vector3 GetWorldPositionFromTouch(Vector2 screenPosition)
    {
        Camera cam = Camera.main;
        if (cam == null) return Vector3.zero;

        Ray ray = cam.ScreenPointToRay(screenPosition);
        if (Physics.Raycast(ray, out RaycastHit hit))
        {
            if (hit.collider.GetComponent<Terrain>())
                return hit.point;
        }
        return Vector3.zero;
    }

    void ApplyTextureToSelectedLayer()
    {
        if (terrainData == null) return;

        // Validate layer index
        if (selectedLayerIndex < 0 || selectedLayerIndex >= maxLayers)
        {
            Debug.LogWarning($"Invalid layer index: {selectedLayerIndex}. Valid range: 0-{maxLayers - 1}");
            return;
        }

        // Apply texture to entire terrain for selected layer
        ApplyTextureToEntireTerrain(selectedLayerIndex);
    }

    void ApplyTextureToEntireTerrain(int layerIndex)
    {
        int alphamapWidth = terrainData.alphamapWidth;
        int alphamapHeight = terrainData.alphamapHeight;

        float[,,] alphaMaps = terrainData.GetAlphamaps(0, 0, alphamapWidth, alphamapHeight);

        // Mobile optimization - process in chunks
        int chunkSize = 64; // Process 64x64 chunks for better performance

        for (int startX = 0; startX < alphamapWidth; startX += chunkSize)
        {
            for (int startZ = 0; startZ < alphamapHeight; startZ += chunkSize)
            {
                int endX = Mathf.Min(startX + chunkSize, alphamapWidth);
                int endZ = Mathf.Min(startZ + chunkSize, alphamapHeight);

                for (int x = startX; x < endX; x++)
                {
                    for (int z = startZ; z < endZ; z++)
                    {
                        // Set selected layer to full strength, others to zero
                        for (int layer = 0; layer < terrainData.alphamapLayers; layer++)
                        {
                            alphaMaps[x, z, layer] = (layer == layerIndex) ? 1f : 0f;
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(0, 0, alphaMaps);
    }

    void PaintTextureAtPosition(Vector3 worldPos)
    {
        if (terrainData == null) return;

        Vector3 terrainPos = worldPos - targetTerrain.transform.position;

        // Convert world position to alphamap coordinates
        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        int radius = Mathf.RoundToInt(paintRadius);
        int size = radius * 2;

        // Clamp to terrain bounds
        int startX = Mathf.Clamp(mapX - radius, 0, terrainData.alphamapWidth - size);
        int startZ = Mathf.Clamp(mapZ - radius, 0, terrainData.alphamapHeight - size);

        // Ensure size doesn't exceed bounds
        size = Mathf.Min(size, terrainData.alphamapWidth - startX);
        size = Mathf.Min(size, terrainData.alphamapHeight - startZ);

        if (size <= 0) return;

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, size, size);

        // Paint with circular brush
        Vector2 center = new Vector2(radius, radius);

        for (int x = 0; x < size; x++)
        {
            for (int z = 0; z < size; z++)
            {
                Vector2 pos = new Vector2(x, z);
                float distance = Vector2.Distance(pos, center);

                if (distance <= radius)
                {
                    // Calculate brush falloff
                    float falloff = 1f - (distance / radius);
                    falloff = Mathf.Pow(falloff, 2f); // Smooth falloff

                    float strength = brushStrength * falloff;

                    // Apply texture only to selected layer
                    for (int layer = 0; layer < terrainData.alphamapLayers; layer++)
                    {
                        if (layer == selectedLayerIndex)
                        {
                            alphaMaps[x, z, layer] = Mathf.Lerp(alphaMaps[x, z, layer], 1f, strength);
                        }
                        else
                        {
                            alphaMaps[x, z, layer] = Mathf.Lerp(alphaMaps[x, z, layer], 0f, strength);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }

    // Public methods for external control
    public void SetSelectedLayer(int layerIndex)
    {
        selectedLayerIndex = Mathf.Clamp(layerIndex, 0, maxLayers - 1);
    }

    public void ApplyCurrentLayerToTerrain()
    {
        applyTextureToSelectedLayer = Random.Range(1, 1000); // Trigger application
    }

    public int GetMaxLayers()
    {
        return maxLayers;
    }

    public string GetLayerInfo()
    {
        if (terrainData == null) return "No terrain data";
        return $"Selected Layer: {selectedLayerIndex}/{maxLayers - 1}";
    }
}
